import { Link } from 'react-router-dom';

const MobileMenu = ({ isLogin, genres, showGenresMobile, onToggleGenres, onMenuClose, onLogout }) => {
  return (
    <div className="md:hidden absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2">
      {isLogin ? (
        <>
          <Link to="/profile" onClick={onMenuClose} className="block px-4 py-2 text-gray-700 hover:bg-gray-100">
            <PERSON><PERSON> sơ
          </Link>
          <Link to="/profile/favorites" onClick={onMenuClose} className="block px-4 py-2 text-gray-700 hover:bg-gray-100">
            Y<PERSON><PERSON> thích
          </Link>
          <Link to="/profile/reading-history" onClick={onMenuClose} className="block px-4 py-2 text-gray-700 hover:bg-gray-100">
            <PERSON><PERSON><PERSON> sử đọc
          </Link>
          <hr className="my-2" />
          <button onClick={onLogout} className="block w-full text-left px-4 py-2 text-red-600 hover:bg-gray-100">
            <PERSON><PERSON>ng xuất
          </button>
        </>
      ) : (
        <>
          <Link to="/login" onClick={onMenuClose} className="block px-4 py-2 text-gray-700 hover:bg-gray-100">
            Đăng nhập
          </Link>
          <Link to="/register" onClick={onMenuClose} className="block px-4 py-2 text-gray-700 hover:bg-gray-100">
            Đăng ký
          </Link>
        </>
      )}
      
      <hr className="my-2" />
      
      <button onClick={onToggleGenres} className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100">
        Thể loại
      </button>
      
      {showGenresMobile && (
        <div className="pl-4">
          {genres.map((genre) => (
            <Link
              key={genre.id}
              to={`/genre/${genre.name}`}
              onClick={onMenuClose}
              className="block px-4 py-1 text-sm text-gray-600 hover:bg-gray-100"
            >
              {genre.name}
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};

export default MobileMenu;
