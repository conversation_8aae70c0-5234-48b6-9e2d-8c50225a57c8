/**
 * <PERSON><PERSON><PERSON> một chuỗi vào localStorage
 * @param {string} key Kh<PERSON>a để lưu trữ
 * @param {string} value Giá trị cần lưu
 */
export const saveString = (key, value) => {
    localStorage.setItem(key, value);
}

/**
 * Lấy một chuỗi từ localStorage
 * @param {string} key Khóa để lấy giá trị
 * @returns {string|null} Giá trị được lưu hoặc null nếu không tồn tại
 */
export const getString = (key) => {
    return localStorage.getItem(key);
}

/**
 * Lưu một đối tượng vào localStorage
 * @param {string} key Kh<PERSON>a để lưu trữ
 * @param {object} value Đối tượng cần lưu
 */
export const saveObject = (key, value) => {
    localStorage.setItem(key, JSON.stringify(value));
}

/**
 * L<PERSON>y một đối tượng từ localStorage
 * @param {string} key Khóa để lấy giá trị
 * @returns {object|null} Đ<PERSON>i tượng được lưu hoặc null nếu không tồn tại
 */
export const getObject = (key) => {
    const item = localStorage.getItem(key);
    if (item) {
        return JSON.parse(item);
    }
    return null;
}

/**
 * Xóa một mục từ localStorage
 * @param {string} key Khóa cần xóa
 */
export const removeItem = (key) => {
    localStorage.removeItem(key);
}
