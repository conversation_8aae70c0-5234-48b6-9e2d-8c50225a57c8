import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';

/**
 * Safely format a date string to relative time
 * @param {string} dateString The date string to format
 * @param {object} options Options for formatDistanceToNow
 * @param {string} fallback Fallback message if date is invalid
 * @returns {string} Formatted relative time string or fallback message
 */
export const safeFormatDistanceToNow = (
    dateString,
    options = { addSuffix: true, locale: vi },
    fallback = 'Chưa cập nhật'
) => {
    if (!dateString) {
        return fallback;
    }

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            console.warn('Invalid date format:', dateString);
            return fallback;
        }
        return formatDistanceToNow(date, options);
    } catch (error) {
        console.warn('Error formatting date:', dateString, error);
        return fallback;
    }
};

/**
 * Safely format a date string to a readable format
 * @param {string} dateString The date string to format
 * @param {string} fallback Fallback message if date is invalid
 * @returns {string} Formatted date string or fallback message
 */
export const safeFormatDate = (
    dateString,
    fallback = 'Chưa cập nhật'
) => {
    if (!dateString) {
        return fallback;
    }

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            console.warn('Invalid date format:', dateString);
            return fallback;
        }
        
        const options = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        
        return date.toLocaleDateString('vi-VN', options);
    } catch (error) {
        console.warn('Error formatting date:', dateString, error);
        return fallback;
    }
};

/**
 * Check if a date string is valid
 * @param {string} dateString The date string to validate
 * @returns {boolean} true if valid, false otherwise
 */
export const isValidDate = (dateString) => {
    if (!dateString) {
        return false;
    }

    try {
        const date = new Date(dateString);
        return !isNaN(date.getTime());
    } catch (error) {
        return false;
    }
};
