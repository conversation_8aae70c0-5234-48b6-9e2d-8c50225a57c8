import { toast } from "react-toastify";

/**
 * Xử lý lỗi từ API
 * @param {any} error Lỗi từ API
 * @param {string} defaultMessage Thông báo mặc định nếu không có thông báo lỗi cụ thể
 */
export const handleApiError = (error, defaultMessage = "Đã xảy ra lỗi") => {
    console.error("API Error:", error);
    
    if (error.isAxiosError) {
        if (error.response) {
            // Lỗi từ server
            const data = error.response.data;
            if (data && data.message) {
                toast.error(data.message, { position: "top-right" });
                return;
            }
        }
    }
    
    // Nếu không có thông báo lỗi cụ thể, hiển thị thông báo mặc định
    toast.error(defaultMessage, { position: "top-right" });
};
